import path from 'node:path'
import MagicString from 'magic-string'
import { withLeadingSlash } from './utils'
import {
  getNodeAssetAttributes,
  nodeIsElement,
  overwriteAttrValue,
  traverseHtml,
} from './utils/html'
import { basePlugin } from './plugins/base'

import type { Token } from 'parse5'
import type { PluginContext } from 'rollup'
import type { Plugin, ResolvedConfig, ViteDevServer } from 'vite'
import type { AssetPlugin, AssetPluginOptions } from './types'

export * from './types'
export * from './plugins/image'

export function assetPlugin(options: AssetPluginOptions = {}): Plugin {
  const { sources = {}, plugins = [] } = options

  const name = 'vite-plugin-asset'

  let viteConfig: ResolvedConfig

  async function transformHtml(html: string, pluginContext?: PluginContext) {
    try {
      const alias = getAlias(viteConfig)
      const s = new MagicString(html)

      const processAsset = async (url: string): Promise<string> => {
        for (const { find, replacement } of alias) {
          if (url.startsWith(find)) {
            const id = withLeadingSlash(url.replace(find, replacement))
            return applyAssetHandler(id, plugins, viteConfig, pluginContext)
          }
        }

        return url
      }

      const tasks: { location: Token.Location; promise: Promise<string> }[] = []

      traverseHtml(html, (node) => {
        if (!nodeIsElement(node)) return

        const assetAttributes = getNodeAssetAttributes(node, sources)

        for (const attr of assetAttributes) {
          tasks.push({
            location: attr.location,
            promise: processAsset(attr.value),
          })
        }
      })

      const promises = tasks.map((task) => task.promise)
      const newValues = await Promise.all(promises)

      tasks.forEach((task, index) => {
        overwriteAttrValue(s, task.location, newValues[index])
      })

      return s.toString()

      // eslint-disable-next-line
    } catch (err: any) {
      err.plugin = name
      throw err
    }
  }

  return {
    name,
    enforce: 'pre',

    configResolved(resolvedConfig) {
      viteConfig = resolvedConfig
    },

    configureServer(server: ViteDevServer) {
      plugins
        .map((plugin) => plugin.middleware!)
        .filter(Boolean)
        .forEach((middleware) => {
          server.middlewares.use(middleware)
        })
    },

    load(id) {},

    transform: {
      filter: { id: /\.html$/ },
      async handler(html) {
        if (viteConfig.command === 'build') {
          return transformHtml(html, this)
        }
      },
    },

    transformIndexHtml: {
      order: 'pre',
      handler(html) {
        if (viteConfig.command === 'serve') {
          return transformHtml(html)
        }
      },
    },
  }
}

async function applyAssetHandler(
  id: string,
  plugins: AssetPlugin[],
  viteConfig: ResolvedConfig,
  pluginContext?: PluginContext,
) {
  for (const { filter, handler } of plugins) {
    const shouldApply =
      typeof filter === 'function' ? filter(id) : filter.test(id)

    if (shouldApply) {
      return handler(id, viteConfig, pluginContext)
    }
  }

  return basePlugin().handler(id, viteConfig, pluginContext)
}

function getAlias(viteConfig: ResolvedConfig) {
  const { alias } = viteConfig.resolve

  return alias
    .filter(({ find }) => typeof find === 'string')
    .map(({ find, replacement }) => {
      const trailingSlash = replacement.endsWith('/') ? '/' : ''
      return {
        find: find as string,
        replacement:
          path.relative(viteConfig.root, replacement) + trailingSlash,
      }
    })
}
