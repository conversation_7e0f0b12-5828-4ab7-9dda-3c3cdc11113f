import path from 'node:path'
import fse from 'fs-extra'
import sharp from 'sharp'
import { URLSearchParams } from 'node:url'
import { hash, generateImageID } from '../utils'
import { normalizePath } from 'vite'
import type {
  WebpOptions,
  JpegOptions,
  PngOptions,
  GifOptions,
  FormatEnum,
} from 'sharp'
import type { ResolvedConfig } from 'vite'
import type { PluginContext } from 'rollup'
import type { AssetPlugin } from '../types'

export interface CacheOptions {
  /**
   * 缓存目录
   * @default '.cache/images'
   */
  dir?: string

  /**
   * 是否启用缓存
   * @default true
   */
  enabled?: boolean
}

export interface AssetImageOptions {
  /**
   * 缓存配置
   */
  cacheOptions?: CacheOptions

  /**
   * 全局 webp 配置
   * @see https://sharp.pixelplumbing.com/api-output/#webp
   */
  webpOptions?: WebpOptions

  /**
   * 全局 jpeg 配置
   * @see https://sharp.pixelplumbing.com/api-output/#jpeg
   */
  jpegOptions?: JpegOptions

  /**
   * 全局 png 配置
   * @see https://sharp.pixelplumbing.com/api-output/#png
   */
  pngOptions?: PngOptions

  /**
   * 全局 gif 配置
   * @see https://sharp.pixelplumbing.com/api-output/#gif
   */
  gifOptions?: GifOptions
}

/** 图片处理参数, 从 url 中的 query 获取 */
interface ProcessingParams {
  /** 压缩质量 (0-100)，默认为 80 */
  quality?: number

  /** 是否启用无损压缩，默认为 false */
  lossless?: boolean

  /** 是否不转换为 webp，默认为 false */
  noWebp?: boolean

  /** 使用原图，不会生成 webp，默认为 false */
  rawImage?: boolean
}

/**
 * 图片处理元数据
 * 包含图片处理过程中所需的所有文件路径、缓冲区数据和处理参数
 */
interface ImageProcessingMetadata {
  /** 源文件图片格式 */
  format: keyof FormatEnum

  /** 源文件的完整路径 */
  sourceFilePath: string

  /** 缓存目录中的原格式文件名 */
  cachedOriginalFileName: string

  /** 缓存目录中的 WebP 格式文件名 */
  cachedWebpFileName: string

  /** 开发环境中显示的文件名 */
  devDisplayFileName: string

  /** 构建输出的原格式文件名 */
  buildOutputFileName: string

  /** 构建输出的 WebP 格式文件名 */
  buildOutputWebpFileName: string

  /** 原始图片的二进制数据 */
  rawImageBuffer: Buffer

  processingParams: ProcessingParams
}

interface ProcessedImageBuffers {
  rawBuffer?: Buffer
  originBuffer?: Buffer
  webpBuffer?: Buffer
}

async function resolveImageInfo(
  id: string,
  viteConfig: ResolvedConfig,
): Promise<ImageProcessingMetadata> {
  const { url, params } = parseId(id)

  const sourceFilePath = path.join(viteConfig.root, url)

  const imageBuffer = await fse.readFile(sourceFilePath)
  const { format } = await sharp(imageBuffer).metadata()
  const imageHash = hash([imageBuffer])
  const imageId = generateImageID(params, imageHash).substring(0, 8)
  const suffix = getSuffix(params)
  const extname = path.extname(url)
  const imageFile = url.split('assets/')[1]
  const imageName = imageFile.replaceAll('/', '-').replace(extname, '')
  const cachedOriginalFileName = `${imageName}-${imageId}${extname}`
  const cachedWebpFileName = `${imageName}-${imageId}.webp`
  const devDisplayFileName = `${url.replace(extname, '')}-${imageId}${
    params.rawImage ? extname : '.webp'
  }`

  return {
    format,
    sourceFilePath,
    cachedOriginalFileName,
    cachedWebpFileName,
    devDisplayFileName,
    buildOutputFileName: `${imageName}${suffix}${extname}`,
    buildOutputWebpFileName: `${imageName}${suffix}.webp`,
    rawImageBuffer: imageBuffer,
    processingParams: params,
  }
}

function parseId(id: string): {
  url: string
  params: ProcessingParams
} {
  const splitUrl = id.split('?')
  const searchParams = new URLSearchParams(splitUrl[1] ?? '')

  const params = {
    quality: 80,
    lossless: false,
    noWebp: false,
    rawImage: false,
  }

  for (const [key, value] of searchParams) {
    switch (key) {
      case 'q':
      case 'quality':
        params.quality = Number(value)
        break
      case 'lossless':
        params.lossless = true
        break
      case 'noWebp':
      case 'no-webp':
        params.noWebp = true
        break
      case 'rawImg':
      case 'rawImage':
      case 'raw-img':
      case 'raw-image':
        params.rawImage = true
        params.noWebp = true
        break
    }
  }

  return {
    url: splitUrl[0],
    params,
  }
}

function getSuffix(params: ProcessingParams) {
  if (params.rawImage) {
    return ''
  }
  let suffix = ''

  if (params.quality) {
    suffix += `-q_${params.quality}`
  }
  if (params.lossless) {
    suffix += `-lossless`
  }
  return suffix
}

async function getProcessedImageBuffers(
  imageInfo: ImageProcessingMetadata,
  options: AssetImageOptions,
  cachePath: string,
): Promise<ProcessedImageBuffers> {
  const {
    format,
    sourceFilePath,
    rawImageBuffer,
    processingParams,
    cachedOriginalFileName,
    cachedWebpFileName,
  } = imageInfo

  const { rawImage, quality, noWebp, lossless } = processingParams

  let originBuffer = undefined
  let webpBuffer = undefined

  if (rawImage) {
    return { rawBuffer: rawImageBuffer }
  }

  const cachedOriginPath = path.join(cachePath, cachedOriginalFileName)
  const cachedWebpPath = path.join(cachePath, cachedWebpFileName)

  const image = sharp(sourceFilePath, { animated: true })

  if (await fse.pathExists(cachedOriginPath)) {
    originBuffer = await fse.readFile(cachedOriginPath)
  } else {
    originBuffer = await image
      .clone()
      .toFormat(format, {
        lossless,
        quality,
      })
      .toBuffer()

    await fse.writeFile(cachedOriginPath, originBuffer)
  }

  if (!noWebp) {
    if (await fse.pathExists(cachedWebpPath)) {
      webpBuffer = await fse.readFile(cachedWebpPath)
    } else {
      webpBuffer = await image
        .clone()
        .webp({
          ...options.webpOptions,
          quality: quality ?? options.webpOptions?.quality,
          lossless: lossless ?? options.webpOptions?.lossless,
        })
        .toBuffer()

      await fse.writeFile(cachedWebpPath, webpBuffer)
    }
  }

  return { originBuffer, webpBuffer }
}

function emitBuildAssets(
  pluginContext: PluginContext,
  viteConfig: ResolvedConfig,
  imageInfo: ImageProcessingMetadata,
  buffers: ProcessedImageBuffers,
) {
  const { processingParams, buildOutputFileName } = imageInfo
  const { originBuffer, webpBuffer, rawBuffer } = buffers
  const { rawImage, noWebp } = processingParams

  if (rawImage) {
    return pluginContext.emitFile({
      type: 'asset',
      source: rawBuffer,
      name: buildOutputFileName,
      originalFileName: normalizePath(
        path.relative(viteConfig.root, buildOutputFileName),
      ),
    })
  }

  let referenceId = pluginContext.emitFile({
    type: 'asset',
    source: originBuffer,
    name: buildOutputFileName,
    originalFileName: normalizePath(
      path.relative(viteConfig.root, buildOutputFileName),
    ),
  })

  if (!noWebp) {
    referenceId = pluginContext.emitFile({
      type: 'asset',
      source: webpBuffer,
      name: buildOutputFileName,
      originalFileName: normalizePath(
        path.relative(viteConfig.root, buildOutputFileName),
      ),
    })
  }

  return referenceId
}

export function imagePlugin(options: AssetImageOptions = {}): AssetPlugin {
  const { cacheOptions = { enabled: true, dir: '.cache/images' } } = options

  const pwd = process.cwd()
  const cachePath = path.join(pwd, cacheOptions.dir!)
  fse.ensureDirSync(cachePath)

  const ASSET_PREFIX = '/@images'
  const assetCache = new Map<
    string,
    ImageProcessingMetadata & { assetUrl: string }
  >()

  return {
    filter: /\.(png|jpg|jpeg|webp|gif)(\?.*)?$/,
    async handler(id, viteConfig, pluginContext) {
      const cached = assetCache.get(id)

      if (cached) {
        return cached.assetUrl
      }

      const imageInfo = await resolveImageInfo(id, viteConfig)

      const { devDisplayFileName } = imageInfo

      let assetUrl = `${ASSET_PREFIX}${devDisplayFileName}`

      if (pluginContext) {
        const buffers = await getProcessedImageBuffers(
          imageInfo,
          options,
          cachePath,
        )

        const referenceId = emitBuildAssets(
          pluginContext,
          viteConfig,
          imageInfo,
          buffers,
        )

        assetUrl = `__VITE_ASSET__${referenceId}__`
      }

      assetCache.set(assetUrl, {
        ...imageInfo,
        assetUrl,
      })

      return assetUrl
    },

    async middleware(req, res, next) {
      if (req.url && req.url.startsWith(ASSET_PREFIX)) {
        const { assetUrl, ...imageInfo } = assetCache.get(req.url)!
        const { rawImage, noWebp } = imageInfo.processingParams
        const buffers = await getProcessedImageBuffers(
          imageInfo,
          options,
          cachePath,
        )

        res.setHeader('Cache-Control', 'public, max-age=86400')

        if (!noWebp) {
          res.setHeader('Content-Type', 'image/webp')
          return res.end(buffers.webpBuffer)
        }

        res.setHeader('Content-Type', `image/${imageInfo.format}`)
        return res.end(rawImage ? buffers.rawBuffer : buffers.originBuffer)
      }

      next()
    },
  }
}
