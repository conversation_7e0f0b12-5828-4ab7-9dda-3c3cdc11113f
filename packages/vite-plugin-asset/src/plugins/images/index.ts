import path from 'node:path'
import fse from 'fs-extra'
import sharp from 'sharp'
import { parseURL } from './parse-url'
import type { ResolvedConfig } from 'vite'
import type { AssetImageOptions } from './types'
import type { AssetPlugin } from '../../types'

export function imagePlugin(options: AssetImageOptions = {}): AssetPlugin {
  const { cacheOptions = { enabled: true, dir: '.cache/images' } } = options

  const pwd = process.cwd()
  const cachePath = path.join(pwd, cacheOptions.dir!)
  fse.ensureDirSync(cachePath)

  const ASSET_PREFIX = '/@images'
  const assetCache = new Map<string, any>()

  return {
    filter: /\.(png|jpg|jpeg|gif)(\?.*)?$/,
    async handler(id, viteConfig, pluginContext) {
      const srcURL = parseURL(id)
      const pathname = decodeURIComponent(srcURL.pathname)

      const { format } = await sharp(pathname).metadata()
      const directives = new URLSearchParams({
        format: `${format};webp`,
        ...Object.fromEntries(srcURL.searchParams),
      })

      // 解析 id, 提取 directives
      // merge directives
      // directives to configs
      // 根据 configs 处理图片
      // 根据图片处理结果 返回 asset url
    },

    async middleware(req, res, next) {
      next()
    },
  }
}

async function resolveConfigs(id: string, viteConfig: ResolvedConfig) {
  const srcURL = parseURL(id)
  const pathname = decodeURIComponent(srcURL.pathname)

  const sourceFilePath = path.join(viteConfig.root, pathname)

  const imageBuffer = await fse.readFile(sourceFilePath)
  const { format } = await sharp(imageBuffer).metadata()

  const configs = []

  const noWebp = srcURL.searchParams.has('no-webp')
  const rawImg = srcURL.searchParams.has('raw-img')
  const lossless = srcURL.searchParams.has('lossless')
  const quality = parseInt(srcURL.searchParams.get('quality') || '80', 10)

  if (rawImg) {
    configs.push({
      format,
      lossless,
      quality,
      source: imageBuffer,
    })
    return configs
  }
  configs.push({
    format,
    lossless,
    quality,
  })
  if (!noWebp) {
    configs.push({
      format: 'webp',
      lossless,
      quality,
    })
  }

  const imageHash = hash([imageBuffer])
  const imageId = generateImageID(params, imageHash).substring(0, 8)
  const suffix = getSuffix(params)
  const extname = path.extname(url)
  const imageFile = url.split('assets/')[1]
  const imageName = imageFile.replaceAll('/', '-').replace(extname, '')
  const cachedOriginalFileName = `${imageName}-${imageId}${extname}`
  const cachedWebpFileName = `${imageName}-${imageId}.webp`
  const devDisplayFileName = `${url.replace(extname, '')}-${imageId}${
    params.rawImage ? extname : '.webp'
  }`

  return {
    format,
    sourceFilePath,
    cachedOriginalFileName,
    cachedWebpFileName,
    devDisplayFileName,
    buildOutputFileName: `${imageName}${suffix}${extname}`,
    buildOutputWebpFileName: `${imageName}${suffix}.webp`,
    rawImageBuffer: imageBuffer,
    processingParams: params,
  }
}
