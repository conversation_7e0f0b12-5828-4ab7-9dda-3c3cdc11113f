import type { TransformFactory } from '../types'
import { getQuality } from './quality'
import { getLossless } from './lossless'
import type { FormatEnum } from 'sharp'

export interface FormatOptions {
  format: keyof FormatEnum
}

export const format: TransformFactory<FormatOptions> = (config) => {
  let format: keyof FormatEnum

  if (!config.format) {
    return
  } else {
    format = config.format
  }

  return function formatTransform(image) {
    return image.toFormat(format, {
      quality: getQuality(config, image),
      lossless: getLossless(config, image),
    })
  }
}
