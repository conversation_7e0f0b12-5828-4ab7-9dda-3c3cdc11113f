import type {
  FormatEnum,
  GifOptions,
  JpegOptions,
  PngOptions,
  WebpOptions,
  Sharp,
  Metadata,
} from 'sharp'

export interface CacheOptions {
  /**
   * 缓存目录
   * @default '.cache/images'
   */
  dir?: string

  /**
   * 是否启用缓存
   * @default true
   */
  enabled?: boolean

  /**
   * 缓存保留时长，以秒为单位，默认是 undefined，即永久保留
   */
  expire?: number
}

export interface AssetImageOptions {
  /**
   * 缓存配置
   */
  cacheOptions?: CacheOptions

  /**
   * 全局 webp 配置
   * @see https://sharp.pixelplumbing.com/api-output/#webp
   */
  webpOptions?: WebpOptions

  /**
   * 全局 jpeg 配置
   * @see https://sharp.pixelplumbing.com/api-output/#jpeg
   */
  jpegOptions?: JpegOptions

  /**
   * 全局 png 配置
   * @see https://sharp.pixelplumbing.com/api-output/#png
   */
  pngOptions?: PngOptions

  /**
   * 全局 gif 配置
   * @see https://sharp.pixelplumbing.com/api-output/#gif
   */
  gifOptions?: GifOptions
}

export interface ImageConfig {
  [key: string]: string | string[]
}

export interface Logger {
  info: (msg: string) => void
  warn: (msg: string) => void
  error: (msg: string) => void
}

export interface TransformFactoryContext {
  useParam: (parameter: string) => void
  searchParams: URLSearchParams
  logger: Logger
}

export type TransformFactory<A = Record<string, unknown>> = (
  metadata: Partial<ImageConfig & A>,
  ctx: TransformFactoryContext,
) => ImageTransformation | undefined

export type TransformOption<A = Record<string, unknown>, T = unknown> = (
  metadata: Partial<ImageConfig & A>,
  image: Sharp,
) => T | undefined

export type ImageTransformation = (image: Sharp) => Sharp | Promise<Sharp>

export interface ImageMetadata extends Metadata {}

/** 图片处理参数, 从 url 中的 query 获取 */
interface ProcessingParams {
  /** 压缩质量 (0-100)，默认为 80 */
  quality?: number

  /** 是否启用无损压缩，默认为 false */
  lossless?: boolean

  /** 是否不转换为 webp，默认为 false */
  noWebp?: boolean

  /** 使用原图，不会生成 webp，默认为 false */
  rawImage?: boolean
}

/**
 * 图片处理元数据
 * 包含图片处理过程中所需的所有文件路径、缓冲区数据和处理参数
 */
interface ImageProcessingMetadata {
  /** 源文件图片格式 */
  format: keyof FormatEnum

  /** 源文件的完整路径 */
  sourceFilePath: string

  /** 缓存目录中的原格式文件名 */
  cachedOriginalFileName: string

  /** 缓存目录中的 WebP 格式文件名 */
  cachedWebpFileName: string

  /** 开发环境中显示的文件名 */
  devDisplayFileName: string

  /** 构建输出的原格式文件名 */
  buildOutputFileName: string

  /** 构建输出的 WebP 格式文件名 */
  buildOutputWebpFileName: string

  /** 原始图片的二进制数据 */
  rawImageBuffer: Buffer

  processingParams: ProcessingParams
}

interface ProcessedImageBuffers {
  rawBuffer?: Buffer
  originBuffer?: Buffer
  webpBuffer?: Buffer
}
