import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import { htmlPlugin } from '@tiga/vite-plugin-html'
import { assetPlugin, imagePlugin } from '@tiga/vite-plugin-asset'
import rpx2CalcPlugin from '@tiga/postcss-rpx2calc'
import buildPlugin from '@tiga/vite-plugin-build'
import autoprefixer from 'autoprefixer'
import UnoCSS from 'unocss/vite'

function flatAssets(p: string, find = 'assets/') {
  if (p.indexOf(find) > 0) {
    const name = p
      .substring(p.indexOf(find) + find.length)
      .split('/')
      .join('-')
    return `assets/${name}`
  }
  return 'assets/[name][extname]'
}

export default defineConfig({
  base: './',
  css: {
    postcss: {
      plugins: [autoprefixer(), rpx2CalcPlugin()],
    },
  },
  plugins: [
    htmlPlugin(),
    assetPlugin({
      sources: {
        img: ['src', 'data-src', 'data-src-mo', 'data-src-pc', 'data-src-pad'],
        video: ['src', 'poster', 'data-src'],
      },
      plugins: [imagePlugin()],
    }),
    UnoCSS(),
    // buildPlugin(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '~images': fileURLToPath(new URL('./src/assets/images', import.meta.url)),
      '~videos': fileURLToPath(new URL('./src/assets/videos', import.meta.url)),
    },
  },
  define: {
    env: excludeEnvs(process.env.__INJECT_ENVS__ || '{}', ['__BUILD_MODE__']),
  },
  build: {
    minify: false,
    cssCodeSplit: false,
    rollupOptions: {
      output: {
        format: 'iife',
        entryFileNames: '[name].js',

        // assetFileNames: (chunkInfo) => {
        //   if (chunkInfo.names[0].match(/\.(css|html)$/)) {
        //     return '[name].[ext]'
        //   }
        //   const fileName = chunkInfo.names[0]

        //   if (fileName && fileName.match(/\.(jpg|png|svg|gif|jpeg|webp)$/)) {
        //     return flatAssets(fileName)
        //   }
        //   return 'assets/[name]-[hash].[ext]'
        // },
      },
    },
  },
})

/**
 * 有些环境变量不用注入到 html 中 防止造成未修改代码的patch打包中 index.js的hash值不一致的问题
 * exclude: __BUILD_MODE__
 */
function excludeEnvs(envs: string, excludeEnvs: string[]) {
  const envsObj = JSON.parse(envs)
  const result: Record<string, unknown> = {}

  if (Object.keys(envsObj).length === 0) {
    return {}
  }

  Object.entries(envsObj).forEach(([key, value]) => {
    if (!excludeEnvs.includes(key)) {
      result[key] = value
    }
  })

  return result
}
